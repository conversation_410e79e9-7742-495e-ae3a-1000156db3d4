/**
 * Illustration Service - Frontend API Integration
 * 
 * This service handles API calls related to illustration type filtering
 * and backend integration for the illustration system.
 */

// API Base URL - should match the existing API configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

/**
 * Interface for the backend response containing allowed illustration type IDs
 */
export interface AllowedIllustrationTypesResponse {
  success: boolean;
  allowedTypeIds: number[]; // Array of illustration type IDs (1-6)
  message?: string;
}

/**
 * Interface for the request payload to get allowed illustration types
 */
export interface IllustrationTypesRequest {
  policyId: number;
  customerId: number;
  policyType?: string;
  customerData?: any; // Additional customer/policy data if needed
}

/**
 * Fetches allowed illustration types from the backend
 *
 * @param requestData - Policy and customer information
 * @returns Promise<AllowedIllustrationTypesResponse>
 */
export const fetchAllowedIllustrationTypes = async (
  requestData: IllustrationTypesRequest
): Promise<AllowedIllustrationTypesResponse> => {
  try {
    // 🚀 BACKEND API CALL - Using GET method as defined in backend filter_routes.py
    const response = await fetch(`${API_BASE_URL}/illustration-options/${requestData.policyId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // The backend returns { policy_details, filtered_tree }
    // We need to extract illustration type IDs from the filtered_tree
    const allowedTypeIds = extractTypeIdsFromFilteredTree(data.filtered_tree);

    return {
      success: true,
      allowedTypeIds: allowedTypeIds,
      message: 'Illustration types filtered successfully'
    };

  } catch (error: any) {
    console.error('Error fetching allowed illustration types from backend:', error);
    // Return empty array when backend fails - no filtering will occur
    return {
      success: false,
      allowedTypeIds: [],
      message: 'Backend API call failed'
    };
  }

};

/**
 * Helper function to extract type IDs from the backend's filtered_tree response
 * The filtered_tree contains illustration types with Display_flag indicating if they should be shown
 */
const extractTypeIdsFromFilteredTree = (filteredTree: any): number[] => {
  const allowedTypeIds: number[] = [];

  if (!filteredTree || typeof filteredTree !== 'object') {
    return allowedTypeIds;
  }

  // Iterate through the filtered tree to find types with Display_flag: true
  for (const [, value] of Object.entries(filteredTree)) {
    if (typeof value === 'object' && value !== null) {
      const typeData = value as any;

      // Check if this is an illustration type with Display_flag
      if (typeData.Ill_type_id && typeData.Display_flag === true) {
        const typeId = parseInt(typeData.Ill_type_id);
        if (typeId >= 1 && typeId <= 6) {
          allowedTypeIds.push(typeId);
        }
      }
    }
  }

  return allowedTypeIds;
};




