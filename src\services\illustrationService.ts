/**
 * Illustration Service - Frontend API Integration
 * 
 * This service handles API calls related to illustration type filtering
 * and backend integration for the illustration system.
 */

// API Base URL - should match the existing API configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

/**
 * Interface for the backend response containing allowed illustration type IDs
 */
export interface AllowedIllustrationTypesResponse {
  success: boolean;
  allowedTypeIds: number[]; // Array of illustration type IDs (1-6)
  message?: string;
}

/**
 * Interface for the request payload to get allowed illustration types
 */
export interface IllustrationTypesRequest {
  policyId: number;
  customerId: number;
  policyType?: string;
  customerData?: any; // Additional customer/policy data if needed
}

/**
 * Fetches allowed illustration types from the backend
 *
 * @param requestData - Policy and customer information
 * @returns Promise<AllowedIllustrationTypesResponse>
 */
export const fetchAllowedIllustrationTypes = async (
  requestData: IllustrationTypesRequest
): Promise<AllowedIllustrationTypesResponse> => {
  try {
    // 🚀 BACKEND API CALL - YOU WILL REPLACE THIS URL WITH YOUR ACTUAL ENDPOINT
    const response = await fetch(`${API_BASE_URL}/illustration-options/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: AllowedIllustrationTypesResponse = await response.json();

    if (!data.success || !Array.isArray(data.allowedTypeIds)) {
      throw new Error('Invalid response format from backend');
    }

    // Validate that type IDs are within expected range (1-6)
    const validTypeIds = data.allowedTypeIds.filter(id => id >= 1 && id <= 6);

    return {
      ...data,
      allowedTypeIds: validTypeIds
    };

  } catch (error: any) {
    console.error('Error fetching allowed illustration types from backend:', error);
    // Return empty array when backend fails - no filtering will occur
    return {
      success: false,
      allowedTypeIds: [],
      message: 'Backend API call failed'
    };
  }

};




